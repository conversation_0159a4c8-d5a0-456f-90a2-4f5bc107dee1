# Postman API Testing Guide

## Base Configuration
- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json` (for POST requests)

---

## 1. Health Check

### Endpoint
```
GET /health
```

### Headers
```
None required
```

### Body
```
None
```

### Expected Response
```json
{
  "status": "OK",
  "timestamp": "2025-06-04T12:03:54.460Z",
  "model": "llama-3.1-8b-instant"
}
```

### Response Status
- **200 OK**: Server is healthy and running

---

## 2. Start New Chat

### Endpoint
```
POST /api/chat
```

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "message": "Hello! Can you explain quantum computing in simple terms?",
  "systemPrompt": "You are a helpful AI assistant specializing in technology and science."
}
```

### Expected Response
```json
{
  "conversationId": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e",
  "message": "Quantum computing is a revolutionary technology that uses the principles of quantum mechanics to process information in fundamentally different ways than classical computers...",
  "timestamp": "2025-06-04T12:04:04.972Z",
  "messageCount": 2
}
```

### Response Status
- **200 OK**: Message sent successfully
- **400 Bad Request**: Invalid message format
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Notes
- Save the `conversationId` for continuing the conversation
- `systemPrompt` is optional for new conversations
- `messageCount` includes system, user, and assistant messages

---

## 3. Continue Existing Chat

### Endpoint
```
POST /api/chat
```

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "message": "Can you give me a practical example of quantum computing?",
  "conversationId": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e"
}
```

### Expected Response
```json
{
  "conversationId": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e",
  "message": "A great practical example is quantum cryptography for secure communications. Companies like IBM and Google are using quantum computers to...",
  "timestamp": "2025-06-04T12:05:15.123Z",
  "messageCount": 4
}
```

### Response Status
- **200 OK**: Message sent successfully
- **404 Not Found**: Conversation ID doesn't exist
- **400 Bad Request**: Invalid request format

### Notes
- Use the `conversationId` from previous chat responses
- Do not include `systemPrompt` when continuing a conversation

---

## 4. Get All Conversations

### Endpoint
```
GET /api/conversations
```

### Headers
```
None required
```

### Body
```
None
```

### Expected Response
```json
{
  "conversations": [
    {
      "id": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e",
      "createdAt": "2025-06-04T12:04:04.357Z",
      "updatedAt": "2025-06-04T12:05:15.123Z",
      "messageCount": 4,
      "lastMessage": "A great practical example is quantum cryptography for secure communications. Companies like IBM..."
    },
    {
      "id": "bc447352-3b3a-4431-b60e-eeae6959990c",
      "createdAt": "2025-06-04T12:05:44.269Z",
      "updatedAt": "2025-06-04T12:05:44.456Z",
      "messageCount": 2,
      "lastMessage": "Hello. How can I assist you today?..."
    }
  ],
  "total": 2
}
```

### Response Status
- **200 OK**: Conversations retrieved successfully

### Notes
- Returns conversations sorted by most recent update
- `lastMessage` is truncated to 100 characters

---

## 5. Get Specific Conversation

### Endpoint
```
GET /api/conversations/{conversationId}
```

### Example URL
```
GET /api/conversations/e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e
```

### Headers
```
None required
```

### Body
```
None
```

### Expected Response
```json
{
  "id": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e",
  "createdAt": "2025-06-04T12:04:04.357Z",
  "updatedAt": "2025-06-04T12:05:15.123Z",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant specializing in technology and science.",
      "timestamp": "2025-06-04T12:04:04.357Z"
    },
    {
      "role": "user",
      "content": "Hello! Can you explain quantum computing in simple terms?",
      "timestamp": "2025-06-04T12:04:04.358Z"
    },
    {
      "role": "assistant",
      "content": "Quantum computing is a revolutionary technology...",
      "timestamp": "2025-06-04T12:04:04.972Z"
    },
    {
      "role": "user",
      "content": "Can you give me a practical example of quantum computing?",
      "timestamp": "2025-06-04T12:05:14.890Z"
    },
    {
      "role": "assistant",
      "content": "A great practical example is quantum cryptography...",
      "timestamp": "2025-06-04T12:05:15.123Z"
    }
  ]
}
```

### Response Status
- **200 OK**: Conversation found and returned
- **404 Not Found**: Conversation ID doesn't exist

### Notes
- Returns complete conversation history with all messages
- Messages include role, content, and timestamp

---

## 6. Create New Empty Conversation

### Endpoint
```
POST /api/conversations
```

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "systemPrompt": "You are a creative writing assistant who helps with storytelling and character development."
}
```

### Expected Response
```json
{
  "id": "f8d2a1b3-4c5e-6f7g-8h9i-0j1k2l3m4n5o",
  "createdAt": "2025-06-04T12:10:30.456Z",
  "message": "Conversation created successfully"
}
```

### Response Status
- **201 Created**: Conversation created successfully
- **500 Internal Server Error**: Server error

### Notes
- `systemPrompt` is optional
- Creates an empty conversation ready for messages
- Returns the new conversation ID

---

## 7. Get Conversation Messages

### Endpoint
```
GET /api/conversations/{conversationId}/messages
```

### Example URL
```
GET /api/conversations/e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e/messages
```

### Headers
```
None required
```

### Body
```
None
```

### Expected Response
```json
{
  "conversationId": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant specializing in technology and science.",
      "timestamp": "2025-06-04T12:04:04.357Z"
    },
    {
      "role": "user",
      "content": "Hello! Can you explain quantum computing in simple terms?",
      "timestamp": "2025-06-04T12:04:04.358Z"
    },
    {
      "role": "assistant",
      "content": "Quantum computing is a revolutionary technology...",
      "timestamp": "2025-06-04T12:04:04.972Z"
    }
  ],
  "total": 3
}
```

### Response Status
- **200 OK**: Messages retrieved successfully
- **404 Not Found**: Conversation ID doesn't exist

### Notes
- Returns only the messages array with total count
- Same as getting specific conversation but focused on messages

---

## 8. Delete Conversation

### Endpoint
```
DELETE /api/conversations/{conversationId}
```

### Example URL
```
DELETE /api/conversations/e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e
```

### Headers
```
None required
```

### Body
```
None
```

### Expected Response
```json
{
  "message": "Conversation deleted successfully",
  "id": "e7fca17e-c5fe-4a34-a8ae-1d4cd743bc1e"
}
```

### Response Status
- **200 OK**: Conversation deleted successfully
- **404 Not Found**: Conversation ID doesn't exist

### Notes
- Permanently deletes the conversation and all its messages
- Cannot be undone

---

## Error Response Examples

### Rate Limit Error (429)
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again later.",
  "retryAfter": "60"
}
```

### Validation Error (400)
```json
{
  "error": "Message is required and must be a non-empty string"
}
```

### Not Found Error (404)
```json
{
  "error": "Conversation not found"
}
```

### Server Error (500)
```json
{
  "error": "Failed to process chat message",
  "message": "Groq API error: Invalid request"
}
```

---

## Testing Workflow

### Basic Chat Flow
1. **Health Check** → Verify server is running
2. **Start New Chat** → Send first message with system prompt
3. **Continue Chat** → Send follow-up messages using conversation ID
4. **Get Conversations** → View all conversations
5. **Get Specific Conversation** → View full conversation history

### Conversation Management Flow
1. **Create Empty Conversation** → Set up conversation with system prompt
2. **Send Messages** → Use the conversation ID to send messages
3. **Get Messages** → Retrieve conversation history
4. **Delete Conversation** → Clean up when done

### Error Testing
1. Send empty message → Should get 400 error
2. Use invalid conversation ID → Should get 404 error
3. Send too many requests quickly → Should get 429 error
