'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { chatService } from '../services/chatService';
import { storageService } from '../services/storageService';
import { MESSAGE_ROLES, MESSAGE_STATUS } from '../utils/constants';
import { handleApiError, generateId } from '../utils/helpers';

export const useChat = () => {
  const [messages, setMessages] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState(null);
  const [systemPrompt, setSystemPrompt] = useState('');
  
  const abortControllerRef = useRef(null);

  // Initialize chat
  useEffect(() => {
    const savedConversationId = storageService.getCurrentConversation();
    if (savedConversationId) {
      loadConversation(savedConversationId);
    }
  }, []);

  // Load conversation from API
  const loadConversation = useCallback(async (conversationId) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const conversation = await chatService.getConversation(conversationId);
      setMessages(conversation.messages || []);
      setCurrentConversationId(conversationId);
      storageService.setCurrentConversation(conversationId);
      
      // Extract system prompt if exists
      const systemMessage = conversation.messages?.find(m => m.role === MESSAGE_ROLES.SYSTEM);
      if (systemMessage) {
        setSystemPrompt(systemMessage.content);
      }
      
    } catch (error) {
      console.error('Failed to load conversation:', error);
      setError(handleApiError(error));
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (messageContent, newSystemPrompt = null) => {
    if (!messageContent.trim()) return;

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const tempMessageId = generateId();
    const userMessage = {
      id: tempMessageId,
      role: MESSAGE_ROLES.USER,
      content: messageContent.trim(),
      timestamp: new Date().toISOString(),
      status: MESSAGE_STATUS.SENDING,
    };

    // Add user message to UI immediately
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setIsTyping(true);
    setError(null);

    try {
      abortControllerRef.current = new AbortController();
      
      let response;
      
      if (currentConversationId) {
        // Continue existing conversation
        response = await chatService.continueChat(messageContent, currentConversationId);
      } else {
        // Start new conversation
        const prompt = newSystemPrompt || systemPrompt;
        response = await chatService.startNewChat(messageContent, prompt);
        setCurrentConversationId(response.conversationId);
        storageService.setCurrentConversation(response.conversationId);
        
        if (prompt) {
          setSystemPrompt(prompt);
        }
      }

      // Update user message status
      setMessages(prev => prev.map(msg => 
        msg.id === tempMessageId 
          ? { ...msg, status: MESSAGE_STATUS.SENT }
          : msg
      ));

      // Add assistant response
      const assistantMessage = {
        id: generateId(),
        role: MESSAGE_ROLES.ASSISTANT,
        content: response.message,
        timestamp: response.timestamp,
        status: MESSAGE_STATUS.SENT,
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Update local storage
      const conversationData = {
        id: response.conversationId,
        lastMessage: response.message.substring(0, 100),
        updatedAt: response.timestamp,
        messageCount: response.messageCount,
      };
      storageService.addConversation(conversationData);

    } catch (error) {
      console.error('Failed to send message:', error);
      
      // Update user message status to error
      setMessages(prev => prev.map(msg => 
        msg.id === tempMessageId 
          ? { ...msg, status: MESSAGE_STATUS.ERROR }
          : msg
      ));
      
      setError(handleApiError(error));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
      abortControllerRef.current = null;
    }
  }, [currentConversationId, systemPrompt]);

  // Start new conversation
  const startNewConversation = useCallback((newSystemPrompt = null) => {
    console.log('Starting new conversation...'); // Debug log
    setMessages([]);
    setCurrentConversationId(null);
    setError(null);
    setIsLoading(false);
    setIsTyping(false);

    if (newSystemPrompt) {
      setSystemPrompt(newSystemPrompt);
    } else {
      // Reset system prompt for truly new conversation
      setSystemPrompt('');
    }

    storageService.clearCurrentConversation();
  }, []);

  // Retry failed message
  const retryMessage = useCallback(async (messageId) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || message.role !== MESSAGE_ROLES.USER) return;

    // Remove the failed message and any subsequent messages
    const messageIndex = messages.findIndex(m => m.id === messageId);
    setMessages(prev => prev.slice(0, messageIndex));

    // Resend the message
    await sendMessage(message.content);
  }, [messages, sendMessage]);

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([]);
    setCurrentConversationId(null);
    setError(null);
    setIsLoading(false);
    setIsTyping(false);
    storageService.clearCurrentConversation();
  }, []);

  // Delete conversation
  const deleteConversation = useCallback(async (conversationId) => {
    try {
      await chatService.deleteConversation(conversationId);
      storageService.removeConversation(conversationId);
      
      if (conversationId === currentConversationId) {
        clearConversation();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw error;
    }
  }, [currentConversationId, clearConversation]);

  // Cancel current request
  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsLoading(false);
    setIsTyping(false);
  }, []);

  // Get conversation stats
  const getConversationStats = useCallback(() => {
    const userMessages = messages.filter(m => m.role === MESSAGE_ROLES.USER);
    const assistantMessages = messages.filter(m => m.role === MESSAGE_ROLES.ASSISTANT);
    
    return {
      totalMessages: messages.length,
      userMessages: userMessages.length,
      assistantMessages: assistantMessages.length,
      hasSystemPrompt: !!systemPrompt,
    };
  }, [messages, systemPrompt]);

  return {
    // State
    messages,
    currentConversationId,
    isLoading,
    isTyping,
    error,
    systemPrompt,
    
    // Actions
    sendMessage,
    loadConversation,
    startNewConversation,
    retryMessage,
    clearConversation,
    deleteConversation,
    cancelRequest,
    setSystemPrompt,
    
    // Utilities
    getConversationStats,
  };
};
