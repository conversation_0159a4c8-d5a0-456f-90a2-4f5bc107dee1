'use client';

import { useState, useEffect, useCallback } from 'react';
import { storageService } from '../services/storageService';

export const useLocalStorage = (key, defaultValue = null) => {
  const [value, setValue] = useState(defaultValue);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize value from localStorage
  useEffect(() => {
    try {
      const storedValue = storageService.getItem(key, defaultValue);
      setValue(storedValue);
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      setValue(defaultValue);
    } finally {
      setIsLoading(false);
    }
  }, [key, defaultValue]);

  // Update localStorage when value changes
  const updateValue = useCallback((newValue) => {
    try {
      setValue(newValue);
      storageService.setItem(key, newValue);
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key]);

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setValue(defaultValue);
      storageService.removeItem(key);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, defaultValue]);

  return [value, updateValue, removeValue, isLoading];
};

// Hook for user preferences
export const useUserPreferences = () => {
  const [preferences, setPreferences] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const userPrefs = storageService.getUserPreferences();
      setPreferences(userPrefs);
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setPreferences(storageService.getUserPreferences()); // Get defaults
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updatePreference = useCallback((key, value) => {
    try {
      const updated = { ...preferences, [key]: value };
      setPreferences(updated);
      storageService.saveUserPreferences(updated);
    } catch (error) {
      console.error('Error updating user preference:', error);
    }
  }, [preferences]);

  const updatePreferences = useCallback((newPreferences) => {
    try {
      const updated = { ...preferences, ...newPreferences };
      setPreferences(updated);
      storageService.saveUserPreferences(updated);
    } catch (error) {
      console.error('Error updating user preferences:', error);
    }
  }, [preferences]);

  const resetPreferences = useCallback(() => {
    try {
      const defaults = storageService.getUserPreferences();
      setPreferences(defaults);
      storageService.saveUserPreferences(defaults);
    } catch (error) {
      console.error('Error resetting user preferences:', error);
    }
  }, []);

  return {
    preferences,
    isLoading,
    updatePreference,
    updatePreferences,
    resetPreferences,
  };
};

// Hook for theme management
export const useTheme = () => {
  const [theme, setTheme] = useState('dark');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const savedTheme = storageService.getTheme();
      setTheme(savedTheme);
      
      // Apply theme to document
      document.documentElement.setAttribute('data-theme', savedTheme);
    } catch (error) {
      console.error('Error loading theme:', error);
      setTheme('dark');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateTheme = useCallback((newTheme) => {
    try {
      setTheme(newTheme);
      storageService.setTheme(newTheme);
      document.documentElement.setAttribute('data-theme', newTheme);
    } catch (error) {
      console.error('Error updating theme:', error);
    }
  }, []);

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    updateTheme(newTheme);
  }, [theme, updateTheme]);

  return {
    theme,
    isLoading,
    updateTheme,
    toggleTheme,
  };
};

// Hook for managing conversation history in localStorage
export const useConversationHistory = () => {
  const [conversations, setConversations] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const savedConversations = storageService.getConversations();
      const currentId = storageService.getCurrentConversation();
      
      setConversations(savedConversations);
      setCurrentConversationId(currentId);
    } catch (error) {
      console.error('Error loading conversation history:', error);
      setConversations([]);
      setCurrentConversationId(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addConversation = useCallback((conversation) => {
    try {
      const updated = [conversation, ...conversations.filter(c => c.id !== conversation.id)];
      setConversations(updated);
      storageService.saveConversations(updated);
    } catch (error) {
      console.error('Error adding conversation:', error);
    }
  }, [conversations]);

  const updateConversation = useCallback((conversationId, updates) => {
    try {
      const updated = conversations.map(conv =>
        conv.id === conversationId ? { ...conv, ...updates } : conv
      );
      setConversations(updated);
      storageService.saveConversations(updated);
    } catch (error) {
      console.error('Error updating conversation:', error);
    }
  }, [conversations]);

  const removeConversation = useCallback((conversationId) => {
    try {
      const updated = conversations.filter(conv => conv.id !== conversationId);
      setConversations(updated);
      storageService.saveConversations(updated);
      
      if (currentConversationId === conversationId) {
        setCurrentConversationId(null);
        storageService.clearCurrentConversation();
      }
    } catch (error) {
      console.error('Error removing conversation:', error);
    }
  }, [conversations, currentConversationId]);

  const setCurrentConversation = useCallback((conversationId) => {
    try {
      setCurrentConversationId(conversationId);
      storageService.setCurrentConversation(conversationId);
    } catch (error) {
      console.error('Error setting current conversation:', error);
    }
  }, []);

  const clearAllConversations = useCallback(() => {
    try {
      setConversations([]);
      setCurrentConversationId(null);
      storageService.saveConversations([]);
      storageService.clearCurrentConversation();
    } catch (error) {
      console.error('Error clearing conversations:', error);
    }
  }, []);

  return {
    conversations,
    currentConversationId,
    isLoading,
    addConversation,
    updateConversation,
    removeConversation,
    setCurrentConversation,
    clearAllConversations,
  };
};
