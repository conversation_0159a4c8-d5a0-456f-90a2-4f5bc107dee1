'use client';

import { useState, useCallback, useEffect } from 'react';
import { chatService } from '../services/chatService';
import { storageService } from '../services/storageService';
import { handleApiError, sortByDate } from '../utils/helpers';

export const useConversations = () => {
  const [conversations, setConversations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  // Load conversations from API and merge with local storage
  const loadConversations = useCallback(async (forceRefresh = false) => {
    // Don't refetch if we've fetched recently and not forcing refresh
    if (!forceRefresh && lastFetch && Date.now() - lastFetch < 30000) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Get conversations from API
      const response = await chatService.getAllConversations();
      const apiConversations = response.conversations || [];

      // Get local conversations
      const localConversations = storageService.getConversations();

      // Merge API and local conversations, preferring API data
      const mergedConversations = [];
      const processedIds = new Set();

      // Add API conversations first
      apiConversations.forEach(apiConv => {
        mergedConversations.push({
          ...apiConv,
          source: 'api',
        });
        processedIds.add(apiConv.id);
      });

      // Add local conversations that aren't in API
      localConversations.forEach(localConv => {
        if (!processedIds.has(localConv.id)) {
          mergedConversations.push({
            ...localConv,
            source: 'local',
          });
        }
      });

      // Sort by most recent
      const sortedConversations = sortByDate(mergedConversations, 'updatedAt');
      
      setConversations(sortedConversations);
      setLastFetch(Date.now());

      // Update local storage with merged data
      storageService.saveConversations(sortedConversations);

    } catch (error) {
      console.error('Failed to load conversations:', error);
      setError(handleApiError(error));
      
      // Fallback to local storage if API fails
      const localConversations = storageService.getConversations();
      setConversations(sortByDate(localConversations, 'updatedAt'));
    } finally {
      setIsLoading(false);
    }
  }, [lastFetch]);

  // Initialize conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Create new conversation
  const createConversation = useCallback(async (systemPrompt = null) => {
    try {
      setError(null);
      
      const response = await chatService.createConversation(systemPrompt);
      
      const newConversation = {
        id: response.id,
        createdAt: response.createdAt,
        updatedAt: response.createdAt,
        messageCount: systemPrompt ? 1 : 0,
        lastMessage: systemPrompt ? 'System prompt set' : 'New conversation',
        source: 'api',
      };

      setConversations(prev => [newConversation, ...prev]);
      storageService.addConversation(newConversation);

      return newConversation;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      setError(handleApiError(error));
      throw error;
    }
  }, []);

  // Delete conversation
  const deleteConversation = useCallback(async (conversationId) => {
    try {
      setError(null);
      
      await chatService.deleteConversation(conversationId);
      
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      storageService.removeConversation(conversationId);
      
      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      setError(handleApiError(error));
      throw error;
    }
  }, []);

  // Update conversation locally
  const updateConversation = useCallback((conversationId, updates) => {
    setConversations(prev => 
      prev.map(conv => 
        conv.id === conversationId 
          ? { ...conv, ...updates, updatedAt: new Date().toISOString() }
          : conv
      )
    );
    
    storageService.updateConversation(conversationId, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });
  }, []);

  // Get conversation by ID
  const getConversation = useCallback((conversationId) => {
    return conversations.find(conv => conv.id === conversationId);
  }, [conversations]);

  // Search conversations
  const searchConversations = useCallback((query) => {
    if (!query.trim()) return conversations;
    
    const searchTerm = query.toLowerCase();
    return conversations.filter(conv => 
      conv.lastMessage?.toLowerCase().includes(searchTerm) ||
      conv.id.toLowerCase().includes(searchTerm)
    );
  }, [conversations]);

  // Filter conversations by date range
  const filterConversationsByDate = useCallback((startDate, endDate) => {
    return conversations.filter(conv => {
      const convDate = new Date(conv.updatedAt);
      return convDate >= startDate && convDate <= endDate;
    });
  }, [conversations]);

  // Get conversation statistics
  const getConversationStats = useCallback(() => {
    const totalConversations = conversations.length;
    const totalMessages = conversations.reduce((sum, conv) => sum + (conv.messageCount || 0), 0);
    
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const weekStart = new Date(todayStart.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);

    const todayConversations = conversations.filter(conv => 
      new Date(conv.updatedAt) >= todayStart
    ).length;

    const weekConversations = conversations.filter(conv => 
      new Date(conv.updatedAt) >= weekStart
    ).length;

    const monthConversations = conversations.filter(conv => 
      new Date(conv.updatedAt) >= monthStart
    ).length;

    return {
      total: totalConversations,
      totalMessages,
      today: todayConversations,
      thisWeek: weekConversations,
      thisMonth: monthConversations,
    };
  }, [conversations]);

  // Export conversations
  const exportConversations = useCallback(async () => {
    try {
      const exportData = {
        conversations,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Failed to export conversations:', error);
      throw error;
    }
  }, [conversations]);

  // Import conversations
  const importConversations = useCallback(async (importData) => {
    try {
      const data = typeof importData === 'string' ? JSON.parse(importData) : importData;
      
      if (!data.conversations || !Array.isArray(data.conversations)) {
        throw new Error('Invalid import data format');
      }

      // Merge with existing conversations
      const existingIds = new Set(conversations.map(conv => conv.id));
      const newConversations = data.conversations.filter(conv => !existingIds.has(conv.id));
      
      const mergedConversations = [...conversations, ...newConversations];
      const sortedConversations = sortByDate(mergedConversations, 'updatedAt');
      
      setConversations(sortedConversations);
      storageService.saveConversations(sortedConversations);
      
      return {
        imported: newConversations.length,
        total: sortedConversations.length,
      };
    } catch (error) {
      console.error('Failed to import conversations:', error);
      throw error;
    }
  }, [conversations]);

  // Clear all conversations
  const clearAllConversations = useCallback(() => {
    setConversations([]);
    storageService.saveConversations([]);
  }, []);

  return {
    // State
    conversations,
    isLoading,
    error,
    
    // Actions
    loadConversations,
    createConversation,
    deleteConversation,
    updateConversation,
    clearAllConversations,
    
    // Utilities
    getConversation,
    searchConversations,
    filterConversationsByDate,
    getConversationStats,
    exportConversations,
    importConversations,
  };
};
