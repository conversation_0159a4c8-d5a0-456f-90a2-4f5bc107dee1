@import "tailwindcss";
@import "./styles/animations.css";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* KaziGPT Theme Variables */
:root {
  --primary-blue: #3b82f6;
  --primary-purple: #8b5cf6;
  --neon-cyan: #00d4ff;
  --neon-green: #00ff88;
  --gray-950: #0a0a0a;
  --gray-900: #1a1a1a;
  --gray-800: #2a2a2a;
  --gray-700: #374151;
  --gray-600: #4b5563;
  --gray-500: #6b7280;
  --gray-400: #9ca3af;
  --gray-300: #d1d5db;
}

[data-theme="light"] {
  --background: #ffffff;
  --foreground: #171717;
  --gray-950: #ffffff;
  --gray-900: #f9fafb;
  --gray-800: #f3f4f6;
  --gray-700: #e5e7eb;
  --gray-600: #d1d5db;
  --gray-500: #9ca3af;
  --gray-400: #6b7280;
  --gray-300: #4b5563;
}

* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-800);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-600);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Selection */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: inherit;
}

/* Focus styles */
.focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Prose styles for markdown content */
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
}

.prose p:last-child {
  margin-bottom: 0;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.prose h1 { font-size: 1.5rem; }
.prose h2 { font-size: 1.25rem; }
.prose h3 { font-size: 1.125rem; }

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

.prose code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.prose pre {
  background: var(--gray-800);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose pre code {
  background: none;
  padding: 0;
}

.prose blockquote {
  border-left: 4px solid var(--primary-blue);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--gray-300);
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.prose th, .prose td {
  border: 1px solid var(--gray-600);
  padding: 0.5rem;
  text-align: left;
}

.prose th {
  background: var(--gray-800);
  font-weight: 600;
}

/* Utility classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.neon-glow {
  box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
