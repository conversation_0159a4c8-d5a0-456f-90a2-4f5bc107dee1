// KaziGPT Storage Service - Local Storage Management
class StorageService {
  constructor() {
    this.STORAGE_KEYS = {
      CONVERSATIONS: 'kazigpt_conversations',
      CURRENT_CONVERSATION: 'kazigpt_current_conversation',
      USER_PREFERENCES: 'kazigpt_user_preferences',
      THEME: 'kazigpt_theme',
    };
  }

  // Check if localStorage is available
  isStorageAvailable() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Generic storage methods
  setItem(key, value) {
    if (!this.isStorageAvailable()) return false;
    
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Storage setItem error:', error);
      return false;
    }
  }

  getItem(key, defaultValue = null) {
    if (!this.isStorageAvailable()) return defaultValue;
    
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Storage getItem error:', error);
      return defaultValue;
    }
  }

  removeItem(key) {
    if (!this.isStorageAvailable()) return false;
    
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Storage removeItem error:', error);
      return false;
    }
  }

  // Conversation-specific methods
  saveConversations(conversations) {
    return this.setItem(this.STORAGE_KEYS.CONVERSATIONS, conversations);
  }

  getConversations() {
    return this.getItem(this.STORAGE_KEYS.CONVERSATIONS, []);
  }

  addConversation(conversation) {
    const conversations = this.getConversations();
    const existingIndex = conversations.findIndex(c => c.id === conversation.id);
    
    if (existingIndex >= 0) {
      conversations[existingIndex] = conversation;
    } else {
      conversations.unshift(conversation); // Add to beginning
    }
    
    return this.saveConversations(conversations);
  }

  updateConversation(conversationId, updates) {
    const conversations = this.getConversations();
    const index = conversations.findIndex(c => c.id === conversationId);
    
    if (index >= 0) {
      conversations[index] = { ...conversations[index], ...updates };
      return this.saveConversations(conversations);
    }
    
    return false;
  }

  removeConversation(conversationId) {
    const conversations = this.getConversations();
    const filtered = conversations.filter(c => c.id !== conversationId);
    return this.saveConversations(filtered);
  }

  // Current conversation methods
  setCurrentConversation(conversationId) {
    return this.setItem(this.STORAGE_KEYS.CURRENT_CONVERSATION, conversationId);
  }

  getCurrentConversation() {
    return this.getItem(this.STORAGE_KEYS.CURRENT_CONVERSATION);
  }

  clearCurrentConversation() {
    return this.removeItem(this.STORAGE_KEYS.CURRENT_CONVERSATION);
  }

  // User preferences methods
  saveUserPreferences(preferences) {
    return this.setItem(this.STORAGE_KEYS.USER_PREFERENCES, preferences);
  }

  getUserPreferences() {
    return this.getItem(this.STORAGE_KEYS.USER_PREFERENCES, {
      theme: 'dark',
      fontSize: 'medium',
      sendOnEnter: true,
      showTimestamps: true,
      autoScroll: true,
    });
  }

  updateUserPreference(key, value) {
    const preferences = this.getUserPreferences();
    preferences[key] = value;
    return this.saveUserPreferences(preferences);
  }

  // Theme methods
  setTheme(theme) {
    return this.setItem(this.STORAGE_KEYS.THEME, theme);
  }

  getTheme() {
    return this.getItem(this.STORAGE_KEYS.THEME, 'dark');
  }

  // Clear all data
  clearAllData() {
    Object.values(this.STORAGE_KEYS).forEach(key => {
      this.removeItem(key);
    });
  }

  // Export/Import data
  exportData() {
    const data = {};
    Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
      data[name] = this.getItem(key);
    });
    return data;
  }

  importData(data) {
    try {
      Object.entries(data).forEach(([name, value]) => {
        const key = this.STORAGE_KEYS[name];
        if (key && value !== null) {
          this.setItem(key, value);
        }
      });
      return true;
    } catch (error) {
      console.error('Import data error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService;
