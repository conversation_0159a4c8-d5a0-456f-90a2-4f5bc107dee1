'use client';

import { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, Sparkles } from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import { MESSAGE_ROLES } from '../../utils/constants';
import { scrollToBottom } from '../../utils/helpers';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import InputArea from './InputArea';

const ChatInterface = ({
  conversationId = null,
  className = '',
  onConversationChange,
  onNewConversation,
}) => {
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  const {
    messages,
    currentConversationId,
    isLoading,
    isTyping,
    error,
    systemPrompt,
    sendMessage,
    loadConversation,
    startNewConversation,
    retryMessage,
    cancelRequest,
    setSystemPrompt,
  } = useChat();

  // Load conversation when conversationId changes
  useEffect(() => {
    if (conversationId && conversationId !== currentConversationId) {
      loadConversation(conversationId);
    } else if (conversationId === null && currentConversationId !== null) {
      // If conversationId is explicitly set to null, start a new conversation
      console.log('ConversationId set to null, starting new conversation');
      startNewConversation();
    }
  }, [conversationId, currentConversationId, loadConversation, startNewConversation]);

  // Notify parent of conversation changes
  useEffect(() => {
    if (onConversationChange) {
      onConversationChange(currentConversationId);
    }
  }, [currentConversationId, onConversationChange]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesContainerRef.current) {
      scrollToBottom(messagesContainerRef.current);
    }
  }, [messages, isTyping]);

  const handleSendMessage = async (message, newSystemPrompt = null) => {
    await sendMessage(message, newSystemPrompt);
  };

  const handleRetryMessage = async (messageId) => {
    await retryMessage(messageId);
  };

  const handleNewConversation = () => {
    startNewConversation();
    // Notify parent that we started a new conversation
    if (onConversationChange) {
      onConversationChange(null);
    }
    // Call external handler if provided
    if (onNewConversation) {
      onNewConversation();
    }
  };

  const visibleMessages = messages.filter(msg => msg.role !== MESSAGE_ROLES.SYSTEM);
  const hasMessages = visibleMessages.length > 0;

  return (
    <div className={`flex flex-col h-full bg-gray-950 ${className}`}>
      {/* Chat Header */}
      <div className="flex-shrink-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <Sparkles size={16} className="text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-white">KaziGPT</h1>
              <p className="text-sm text-gray-400">
                {currentConversationId ? 'Active conversation' : 'Start a new conversation'}
              </p>
            </div>
          </div>
          
          {/* New conversation button */}
          <button
            onClick={handleNewConversation}
            className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600"
          >
            New Chat
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-6 py-4 space-y-4"
      >
        {!hasMessages ? (
          /* Welcome Screen */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center h-full text-center space-y-6"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <MessageSquare size={32} className="text-white" />
            </div>
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-white">Welcome to KaziGPT</h2>
              <p className="text-gray-400 max-w-md">
                Your advanced AI assistant is ready to help. Start a conversation by typing a message below.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              {[
                "Explain quantum computing",
                "Write a creative story",
                "Help with coding",
                "Plan a project"
              ].map((suggestion, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleSendMessage(suggestion)}
                  className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-600 text-left transition-colors"
                >
                  <span className="text-white">{suggestion}</span>
                </motion.button>
              ))}
            </div>
          </motion.div>
        ) : (
          /* Messages */
          <div className="space-y-4">
            <AnimatePresence>
              {visibleMessages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  onRetry={handleRetryMessage}
                  showTimestamp={true}
                />
              ))}
            </AnimatePresence>

            {/* Typing Indicator */}
            <AnimatePresence>
              {isTyping && <TypingIndicator />}
            </AnimatePresence>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-center"
              >
                <div className="bg-red-900/20 border border-red-500 rounded-lg px-4 py-2 text-red-400 text-sm">
                  {error}
                </div>
              </motion.div>
            )}
          </div>
        )}
        
        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <InputArea
        onSendMessage={handleSendMessage}
        onCancel={cancelRequest}
        disabled={false}
        loading={isLoading}
        showSystemPrompt={!hasMessages}
        systemPrompt={systemPrompt}
        onSystemPromptChange={setSystemPrompt}
      />
    </div>
  );
};

export default ChatInterface;
