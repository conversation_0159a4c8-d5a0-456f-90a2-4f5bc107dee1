'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Menu } from 'lucide-react';
import Sidebar from '../sidebar/Sidebar';
import ChatInterface from '../chat/ChatInterface';
import Button from '../ui/Button';

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeConversationId, setActiveConversationId] = useState(null);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth >= 1024) {
        setSidebarOpen(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close sidebar on mobile when conversation is selected
  useEffect(() => {
    if (isMobile && activeConversationId) {
      setSidebarOpen(false);
    }
  }, [activeConversationId, isMobile]);

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleConversationSelect = (conversationId) => {
    setActiveConversationId(conversationId);
  };

  const handleNewConversation = () => {
    setActiveConversationId(null);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const handleConversationChange = (conversationId) => {
    setActiveConversationId(conversationId);
  };

  return (
    <div className="flex h-screen bg-gray-950 text-white overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onToggle={handleToggleSidebar}
        activeConversationId={activeConversationId}
        onConversationSelect={handleConversationSelect}
        onNewConversation={handleNewConversation}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        {isMobile && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="lg:hidden bg-gray-900 border-b border-gray-700 p-4"
          >
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleSidebar}
                icon={<Menu size={20} />}
                className="p-2"
              />
              <h1 className="text-lg font-semibold">KaziGPT</h1>
              <div className="w-10" /> {/* Spacer for centering */}
            </div>
          </motion.div>
        )}

        {/* Chat Interface */}
        <div className="flex-1 min-h-0">
          <ChatInterface
            conversationId={activeConversationId}
            onConversationChange={handleConversationChange}
          />
        </div>
      </div>
    </div>
  );
};

export default Layout;
