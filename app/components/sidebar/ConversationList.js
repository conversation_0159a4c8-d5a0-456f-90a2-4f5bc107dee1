'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Calendar, Filter } from 'lucide-react';
import { useConversations } from '../../hooks/useConversations';
import { groupByDate, formatDate } from '../../utils/helpers';
import ConversationItem from './ConversationItem';
import LoadingSpinner from '../ui/LoadingSpinner';

const ConversationList = ({
  activeConversationId,
  onConversationSelect,
  className = '',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterBy, setFilterBy] = useState('all'); // all, today, week, month

  const {
    conversations,
    isLoading,
    error,
    deleteConversation,
    searchConversations,
    filterConversationsByDate,
  } = useConversations();

  // Filter and search conversations
  const filteredConversations = useMemo(() => {
    let filtered = conversations;

    // Apply date filter
    if (filterBy !== 'all') {
      const now = new Date();
      let startDate;

      switch (filterBy) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          startDate = new Date(0);
      }

      filtered = filterConversationsByDate(startDate, now);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = searchConversations(searchQuery);
    }

    return filtered;
  }, [conversations, filterBy, searchQuery, searchConversations, filterConversationsByDate]);

  // Group conversations by date
  const groupedConversations = useMemo(() => {
    return groupByDate(filteredConversations, 'updatedAt');
  }, [filteredConversations]);

  const handleConversationSelect = (conversationId) => {
    if (onConversationSelect) {
      onConversationSelect(conversationId);
    }
  };

  const handleDeleteConversation = async (conversationId) => {
    try {
      await deleteConversation(conversationId);
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw error;
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <LoadingSpinner size="md" color="blue" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-center ${className}`}>
        <p className="text-red-400 text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Search and Filter */}
      <div className="p-4 space-y-3 border-b border-gray-700">
        {/* Search Input */}
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-800 text-white placeholder-gray-400 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex space-x-1">
          {[
            { key: 'all', label: 'All' },
            { key: 'today', label: 'Today' },
            { key: 'week', label: 'Week' },
            { key: 'month', label: 'Month' },
          ].map((filter) => (
            <button
              key={filter.key}
              onClick={() => setFilterBy(filter.key)}
              className={`px-3 py-1 text-xs rounded-md transition-colors ${
                filterBy === filter.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-gray-300'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-gray-400 text-sm">
              {searchQuery.trim() ? 'No conversations found' : 'No conversations yet'}
            </p>
          </div>
        ) : (
          <div className="p-2">
            <AnimatePresence>
              {Object.entries(groupedConversations).map(([date, dateConversations]) => (
                <motion.div
                  key={date}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="mb-4"
                >
                  {/* Date Header */}
                  <div className="flex items-center space-x-2 px-2 py-1 mb-2">
                    <Calendar size={12} className="text-gray-500" />
                    <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      {formatDate(date)}
                    </h3>
                  </div>

                  {/* Conversations for this date */}
                  <div className="space-y-1">
                    {dateConversations.map((conversation, index) => (
                      <motion.div
                        key={conversation.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                      >
                        <ConversationItem
                          conversation={conversation}
                          isActive={conversation.id === activeConversationId}
                          onClick={handleConversationSelect}
                          onDelete={handleDeleteConversation}
                        />
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Stats */}
      {filteredConversations.length > 0 && (
        <div className="p-4 border-t border-gray-700">
          <p className="text-xs text-gray-500 text-center">
            {filteredConversations.length} conversation{filteredConversations.length !== 1 ? 's' : ''}
            {searchQuery.trim() && ' found'}
          </p>
        </div>
      )}
    </div>
  );
};

export default ConversationList;
