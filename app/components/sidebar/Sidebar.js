'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Menu, 
  X, 
  Settings, 
  Download, 
  Upload, 
  Trash2,
  Moon,
  Sun,
  Zap
} from 'lucide-react';
import { APP_CONFIG } from '../../utils/constants';
import { useTheme } from '../../hooks/useLocalStorage';
import ConversationList from './ConversationList';
import Button from '../ui/Button';
import Modal, { ConfirmModal } from '../ui/Modal';

const Sidebar = ({
  isOpen,
  onToggle,
  activeConversationId,
  onConversationSelect,
  onNewConversation,
  className = '',
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [showClearModal, setShowClearModal] = useState(false);
  const { theme, toggleTheme } = useTheme();

  const handleNewConversation = () => {
    if (onNewConversation) {
      onNewConversation();
    }
  };

  const handleExportData = () => {
    // TODO: Implement export functionality
    console.log('Export data');
  };

  const handleImportData = () => {
    // TODO: Implement import functionality
    console.log('Import data');
  };

  const handleClearAllData = () => {
    setShowClearModal(true);
  };

  const confirmClearAllData = () => {
    // TODO: Implement clear all data functionality
    console.log('Clear all data');
    setShowClearModal(false);
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={onToggle}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        className={`fixed left-0 top-0 h-full w-80 bg-gray-900 border-r border-gray-700 z-50 lg:relative lg:translate-x-0 ${className}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex-shrink-0 p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Zap size={16} className="text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white">{APP_CONFIG.NAME}</h1>
                  <p className="text-xs text-gray-400">v{APP_CONFIG.VERSION}</p>
                </div>
              </div>
              
              {/* Close button for mobile */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="lg:hidden p-2"
                icon={<X size={16} />}
              />
            </div>

            {/* New Conversation Button */}
            <Button
              onClick={handleNewConversation}
              className="w-full"
              icon={<Plus size={16} />}
            >
              New Conversation
            </Button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-hidden">
            <ConversationList
              activeConversationId={activeConversationId}
              onConversationSelect={onConversationSelect}
            />
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 p-4 border-t border-gray-700">
            <div className="flex items-center justify-between">
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                className="p-2"
                icon={theme === 'dark' ? <Sun size={16} /> : <Moon size={16} />}
                title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
              />

              {/* Settings Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(true)}
                className="p-2"
                icon={<Settings size={16} />}
                title="Settings"
              />

              {/* Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="p-2 lg:hidden"
                icon={<Menu size={16} />}
                title="Toggle menu"
              />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Settings Modal */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="Settings"
        size="md"
      >
        <div className="space-y-6">
          {/* Theme Settings */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">Appearance</h3>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Theme</span>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleTheme}
                icon={theme === 'dark' ? <Sun size={16} /> : <Moon size={16} />}
              >
                {theme === 'dark' ? 'Light' : 'Dark'}
              </Button>
            </div>
          </div>

          {/* Data Management */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">Data Management</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">Export conversations</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportData}
                  icon={<Download size={16} />}
                >
                  Export
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">Import conversations</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleImportData}
                  icon={<Upload size={16} />}
                >
                  Import
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">Clear all data</span>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={handleClearAllData}
                  icon={<Trash2 size={16} />}
                >
                  Clear
                </Button>
              </div>
            </div>
          </div>

          {/* About */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">About</h3>
            <div className="text-sm text-gray-300 space-y-1">
              <p><strong>{APP_CONFIG.NAME}</strong> v{APP_CONFIG.VERSION}</p>
              <p>{APP_CONFIG.DESCRIPTION}</p>
            </div>
          </div>
        </div>
      </Modal>

      {/* Clear All Data Confirmation */}
      <ConfirmModal
        isOpen={showClearModal}
        onClose={() => setShowClearModal(false)}
        onConfirm={confirmClearAllData}
        title="Clear All Data"
        message="Are you sure you want to clear all conversations and data? This action cannot be undone."
        confirmText="Clear All"
        cancelText="Cancel"
        variant="danger"
      />
    </>
  );
};

export default Sidebar;
