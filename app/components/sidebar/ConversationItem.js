'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, Trash2, MoreVertical } from 'lucide-react';
import { formatTimestamp, truncateText } from '../../utils/helpers';
import Button from '../ui/Button';
import { ConfirmModal } from '../ui/Modal';

const ConversationItem = ({
  conversation,
  isActive = false,
  onClick,
  onDelete,
  className = '',
}) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleClick = () => {
    if (onClick) {
      onClick(conversation.id);
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      if (onDelete) {
        await onDelete(conversation.id);
      }
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteClick = (e) => {
    e.stopPropagation();
    setShowDeleteModal(true);
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        whileHover={{ x: 4 }}
        className={`group relative cursor-pointer ${className}`}
        onClick={handleClick}
      >
        <div
          className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
            isActive
              ? 'bg-blue-600/20 border border-blue-500/30 shadow-lg'
              : 'hover:bg-gray-800 border border-transparent'
          }`}
        >
          {/* Icon */}
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            isActive
              ? 'bg-blue-500 text-white'
              : 'bg-gray-700 text-gray-400 group-hover:bg-gray-600'
          }`}>
            <MessageSquare size={16} />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <p className={`text-sm font-medium truncate ${
                isActive ? 'text-white' : 'text-gray-300'
              }`}>
                {conversation.lastMessage 
                  ? truncateText(conversation.lastMessage, 30)
                  : 'New conversation'
                }
              </p>
              
              {/* Delete button - only show on hover */}
              <Button
                variant="ghost"
                size="xs"
                onClick={handleDeleteClick}
                className="opacity-0 group-hover:opacity-100 p-1 h-6 w-6 text-gray-500 hover:text-red-400 transition-all"
                icon={<Trash2 size={12} />}
              />
            </div>
            
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-gray-500">
                {conversation.messageCount || 0} messages
              </p>
              <p className="text-xs text-gray-500">
                {formatTimestamp(conversation.updatedAt || conversation.createdAt)}
              </p>
            </div>
          </div>

          {/* Active indicator */}
          {isActive && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-500 rounded-r-full"
            />
          )}
        </div>
      </motion.div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title="Delete Conversation"
        message="Are you sure you want to delete this conversation? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={isDeleting}
      />
    </>
  );
};

export default ConversationItem;
