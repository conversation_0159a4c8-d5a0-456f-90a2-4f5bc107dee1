// KaziGPT Constants
export const APP_CONFIG = {
  NAME: 'KaziGPT',
  VERSION: '1.0.0',
  DESCRIPTION: 'Advanced Futuristic AI Chat Interface',
  API_BASE_URL: 'http://localhost:3000',
};

export const THEMES = {
  DARK: 'dark',
  LIGHT: 'light',
  NEON: 'neon',
};

export const MESSAGE_ROLES = {
  SYSTEM: 'system',
  USER: 'user',
  ASSISTANT: 'assistant',
};

export const MESSAGE_STATUS = {
  SENDING: 'sending',
  SENT: 'sent',
  ERROR: 'error',
  TYPING: 'typing',
};

export const CONVERSATION_STATUS = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
  DELETED: 'deleted',
};

export const UI_CONSTANTS = {
  MAX_MESSAGE_LENGTH: 4000,
  TYPING_DELAY: 50,
  AUTO_SCROLL_DELAY: 100,
  SIDEBAR_WIDTH: 320,
  MOBILE_BREAKPOINT: 768,
};

export const KEYBOARD_SHORTCUTS = {
  NEW_CHAT: 'ctrl+n',
  SEND_MESSAGE: 'enter',
  SEND_MESSAGE_ALT: 'ctrl+enter',
  FOCUS_INPUT: 'ctrl+l',
  TOGGLE_SIDEBAR: 'ctrl+b',
  SEARCH: 'ctrl+f',
};

export const ANIMATIONS = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  EASING: {
    EASE_OUT: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
};

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  RATE_LIMIT: 'Too many requests. Please wait a moment before trying again.',
  INVALID_MESSAGE: 'Please enter a valid message.',
  CONVERSATION_NOT_FOUND: 'Conversation not found.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
};

export const SUCCESS_MESSAGES = {
  MESSAGE_SENT: 'Message sent successfully',
  CONVERSATION_CREATED: 'New conversation created',
  CONVERSATION_DELETED: 'Conversation deleted',
  SETTINGS_SAVED: 'Settings saved successfully',
};

export const SYSTEM_PROMPTS = {
  DEFAULT: 'You are KaziGPT, a helpful AI assistant. Provide clear, accurate, and helpful responses.',
  CREATIVE: 'You are a creative AI assistant specializing in writing, brainstorming, and artistic endeavors.',
  TECHNICAL: 'You are a technical AI assistant specializing in programming, engineering, and problem-solving.',
  EDUCATIONAL: 'You are an educational AI assistant focused on teaching and explaining complex topics clearly.',
  CASUAL: 'You are a friendly and casual AI assistant. Keep responses conversational and approachable.',
};

export const PLACEHOLDER_MESSAGES = {
  INPUT: 'Type your message here...',
  EMPTY_CHAT: 'Start a conversation with KaziGPT',
  NO_CONVERSATIONS: 'No conversations yet',
  LOADING: 'Loading...',
  TYPING: 'KaziGPT is typing...',
};

export const COLORS = {
  PRIMARY: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  NEON: {
    BLUE: '#00d4ff',
    GREEN: '#00ff88',
    PURPLE: '#8b5cf6',
    PINK: '#f472b6',
    YELLOW: '#fbbf24',
  },
  DARK: {
    BG_PRIMARY: '#0a0a0a',
    BG_SECONDARY: '#1a1a1a',
    BG_TERTIARY: '#2a2a2a',
    TEXT_PRIMARY: '#ffffff',
    TEXT_SECONDARY: '#a1a1aa',
    BORDER: '#374151',
  },
};

export const GRADIENTS = {
  NEON_BLUE: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  NEON_GREEN: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  NEON_PURPLE: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  DARK_GLASS: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
};

export const FONT_SIZES = {
  XS: '0.75rem',
  SM: '0.875rem',
  BASE: '1rem',
  LG: '1.125rem',
  XL: '1.25rem',
  '2XL': '1.5rem',
  '3XL': '1.875rem',
  '4XL': '2.25rem',
};

export const BREAKPOINTS = {
  SM: '640px',
  MD: '768px',
  LG: '1024px',
  XL: '1280px',
  '2XL': '1536px',
};
